// //*-- s3 --*/
.s3
  $root: &
  width: 100%
  height: 320px
  position: relative
  background: #fff8e1
  &__content
    display: flex
    align-items: center
    margin: 0 50px
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      text-align: center

  &__title
    font-size: $fontsize-40
    font-weight: $fontweight-light
    color: #333333
    text-align: center
    line-height: 1.2
    m
    @media (max-width: $bk-tb)
      font-size: $fontsize-32


  &__illustration
    position: relative
    display: flex
    padding-left: 100px
    // justify-content: center
    // align-items: center
    bottom: 17px

    img
      // width: 100%
      min-width: 304px
      height: auto
      display: block
      
  &__services
    display: flex
    flex-direction: column
    align-items: center
    margin-right: auto
    padding-left: 43px

  &__service
    border-radius: 25px
    padding: 20px 0px
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    cursor: pointer
    text-align: left
    display: flex
    margin-right: auto

    &:hover
      transform: translateY(-3px)
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15)

    &-label
      display: block
      font-size: $fontsize-26
      // font-weight: $fontweight-bold
      color: #e85d3f
      margin-bottom: 8px

    &-desc
      font-size: $fontsize-18
      color: #333333
      line-height: 1.333333
      margin: auto
