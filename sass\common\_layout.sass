// /*-- layout --*/
body
  font-family: $fontfamily-default
  color: $color-black
  margin: 0 auto
  font-weight: $fontweight-normal
  line-height: 1.2
  position: relative
  overflow-x: hidden
  
html
  overflow-x: hidden

select,button,textarea,input
  font-family: $fontfamily-default
  outline: none

*
  -webkit-box-sizing: border-box
  -moz-box-sizing: border-box
  box-sizing: border-box
  -webkit-tap-highlight-color: transparent
  -webkit-text-size-adjust: none

// *:focus
//   outline: thin dotted $color-blue


table
  border-collapse: collapse

// a
  // color: $color-white
  // +hover-second-o

a,a:focus
  cursor: pointer
  text-decoration: none
  transition: color 300ms

// a:hover,
// a:focus
  // text-decoration: none

// a:hover
//   opacity: 0.8

a:active
  outline: none


// ::selection
//   color: $color-white
//   background: $color-brown


img
  -webkit-touch-callout: none
  -webkit-user-select: none
  -moz-user-select: none
  -ms-user-select: none
  user-select: none
  max-width: 100%

.container
  padding-right: 15px
  padding-left: 15px
  margin-right: auto
  margin-left: auto
  @media(min-width: $bk-min-sm)
    width: 750px
    padding-right: 0px
    padding-left: 0px
  @media(min-width: $bk-min-tb)
    width: 850px
  @media(min-width: $bk-min-md)
    width: 1050px
  @media(min-width: $bk-min-lg)
    width: 1350px