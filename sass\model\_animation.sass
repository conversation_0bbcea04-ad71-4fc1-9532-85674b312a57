// /*-- animation --*/


// Animate.css
.animated
  -webkit-animation-duration: 1s
  animation-duration: 1s
  -webkit-animation-fill-mode: both
  animation-fill-mode: both

@keyframes fadeInDown
  0%
    opacity: 0
    -webkit-transform: translateY(-20px)
    -ms-transform: translateY(-20px)
    transform: translateY(-20px)

  100%
    opacity: 1
    -webkit-transform: translateY(0)
    -ms-transform: translateY(0)
    transform: translateY(0)

.fadeInDown
  -webkit-animation-name: fadeInDown
  animation-name: fadeInDown

@keyframes zoomIn
  from
    opacity: 0
    transform: scale(0)
  100%
    opacity: 1
    transform: scale(1)

.zoomIn
  -webkit-animation-name: zoomIn
  animation-name: zoomIn

@keyframes heartbeat
  0%
    transform: scale(1)
  50%
    transform: scale(1.1)
  100%
    transform: scale(1)

.heartbeat
  -webkit-animation-name: heartbeat
  animation-name: heartbeat